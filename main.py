import os
from PIL import Image
import gradio as gr
import time
import zipfile
import tempfile
import shutil
import schedule
import threading
from datetime import datetime, timedelta

# 第二天凌晨清空服务器上的数据
def clear_server_data():
    output_dirs = ['converted_images', 'compressed_images']
    for output_dir in output_dirs:
        if os.path.exists(output_dir):
            shutil.rmtree(output_dir)
    temp_files = [f for f in os.listdir('.') if f.startswith(('converted_', 'compressed_')) and f.endswith('.zip')]
    for temp_file in temp_files:
        os.remove(temp_file)

# 启动定时任务
def schedule_clear_task():
    now = datetime.now()
    next_day = now + timedelta(days=1)
    next_midnight = next_day.replace(hour=0, minute=0, second=0, microsecond=0)
    seconds_until_midnight = (next_midnight - now).total_seconds()
    time.sleep(seconds_until_midnight)
    clear_server_data()
    schedule.every().day.at("00:00").do(clear_server_data)
    while True:
        schedule.run_pending()
        time.sleep(1)

# 启动定时任务线程
threading.Thread(target=schedule_clear_task, daemon=True).start()

# 批量转换（上传/路径）

# 批量转换（上传/路径）
def unified_batch_convert(files, folder_path, output_format, delete_original):
    # Mode 1: File Upload (takes precedence)
    if files:
        output_format = output_format.lower()
        save_format = 'JPEG' if output_format in ('jpg', 'jpeg') else output_format.upper()
        ext = 'jpg' if output_format == 'jpg' else output_format
        
        output_dir = tempfile.mkdtemp()
        try:
            success_count = 0
            error_count = 0
            output_files = []

            for temp_file in files:
                original_filename = os.path.basename(temp_file.name)
                file_base, _ = os.path.splitext(original_filename)
                output_filename = f"{file_base}.{ext}"
                output_path = os.path.join(output_dir, output_filename)

                try:
                    with Image.open(temp_file.name) as img:
                        if save_format == 'JPEG':
                            if img.mode != 'RGB':
                                img = img.convert('RGB')
                        img.save(output_path, save_format)
                    success_count += 1
                    output_files.append(output_path)
                except Exception as e:
                    print(f"转换 {original_filename} 时出错: {e}")
                    error_count += 1
            
            if success_count == 0:
                return None, f"所有 {error_count} 张图片都转换失败。"

            if len(output_files) == 1:
                status_message = f"上传模式处理完成：成功 {success_count} 张，失败 {error_count} 张。"
                return output_files[0], status_message
            else:
                zip_path_obj = tempfile.NamedTemporaryFile(delete=False, suffix=".zip", prefix="converted_")
                zip_path = zip_path_obj.name
                zip_path_obj.close()

                with zipfile.ZipFile(zip_path, 'w') as zipf:
                    for a_file in os.listdir(output_dir):
                        zipf.write(os.path.join(output_dir, a_file), arcname=a_file)
                
                status_message = f"上传模式处理完成：成功 {success_count} 张，失败 {error_count} 张。ZIP文件已生成。"
                return zip_path, status_message

        except Exception as e:
            print(f"批量转换时发生意外错误: {e}")
            return None, "处理过程中发生意外错误。"
        finally:
            # 延迟删除临时目录，确保 Gradio 处理完成
            if os.path.exists(output_dir):
                threading.Timer(10, shutil.rmtree, args=[output_dir]).start()


    # Mode 2: Folder Path
    elif folder_path and os.path.isdir(folder_path):
        output_format = output_format.lower()
        success_count = 0
        error_count = 0
        supported_formats = ['jpg', 'png', 'jpeg', 'bmp', 'gif']
        
        if output_format not in supported_formats:
            return None, f"不支持的输出格式: {output_format}。"

        for root, _, files_in_dir in os.walk(folder_path):
            for file in files_in_dir:
                try:
                    file_ext = os.path.splitext(file)[1].lower()[1:]
                    if file_ext in supported_formats and file_ext != output_format:
                        input_path = os.path.join(root, file)
                        output_path = os.path.splitext(input_path)[0] + f'.{output_format}'
                        
                        with Image.open(input_path) as img:
                            if output_format in ('jpg', 'jpeg'):
                                if img.mode != 'RGB':
                                    img = img.convert('RGB')
                            save_format = 'JPEG' if output_format == 'jpg' else output_format.upper()
                            img.save(output_path, save_format)
                        
                        success_count += 1
                        if delete_original:
                            os.remove(input_path)
                except Exception as e:
                    print(f"转换 {file} 时出错: {e}")
                    error_count += 1
        
        status_message = f"路径模式处理完成：成功转换 {success_count} 张图片，{error_count} 张图片转换失败。"
        return None, status_message
    
    # No valid input
    else:
        return None, "错误：请上传文件，或在高级选项中提供一个有效的本地文件夹路径。"

# 批量压缩（上传/路径）
# 批量压缩（上传/路径）
def compress_images_gradio(files, folder_path, short_side, quality, width=None, height=None, mode="ratio", ratio="50%", custom_output_path=None, custom_output_name=None):
    short_side = int(short_side) if short_side else 0
    quality = int(quality)
    width = int(width) if width else 0
    height = int(height) if height else 0
    ratio = float(ratio.strip('%')) / 100 if ratio != "自定义" else 1.0
    supported_exts = ('.png', '.jpg', '.jpeg', '.gif')
    # 上传模式
    if files:
        output_dir = tempfile.mkdtemp()
        try:
            success_count = 0
            error_count = 0
            output_files = []

            for temp_file in files:
                original_filename = os.path.basename(temp_file.name)
                file_base, ext = os.path.splitext(original_filename)
                output_filename = f"{file_base}{ext}"
                output_path = os.path.join(output_dir, output_filename)
                try:
                    with Image.open(temp_file.name) as img:
                        orig_width, orig_height = img.size
                        if mode == "按比例压缩":
                            new_width = int(orig_width * ratio)
                            new_height = int(orig_height * ratio)
                        else:  # 自定义尺寸模式
                            if width > 0 and height > 0:
                                new_width = width
                                new_height = height
                            elif width > 0:  # only width specified
                                new_height = int(orig_height * (width / orig_width))
                                new_width = width
                            elif height > 0:  # only height specified
                                new_width = int(orig_width * (height / orig_height))
                                new_height = height
                            else:  # fallback to short side mode
                                if orig_width < orig_height:
                                    new_width = short_side
                                    new_height = int(orig_height * (short_side / orig_width))
                                else:
                                    new_height = short_side
                                    new_width = int(orig_width * (short_side / orig_height))
                        resized_img = img.resize((new_width, new_height), Image.LANCZOS)
                        resized_img.save(output_path, optimize=True, quality=quality)
                    success_count += 1
                    output_files.append(output_path)
                except Exception as e:
                    print(f"压缩 {original_filename} 时出错: {e}")
                    error_count += 1
            if success_count == 0:
                return None, f"所有 {error_count} 张图片都压缩失败。"

            if len(output_files) == 1:
                status_message = f"上传模式压缩完成：成功 {success_count} 张，失败 {error_count} 张。"
                return output_files[0], status_message
            else:
                zip_path_obj = tempfile.NamedTemporaryFile(delete=False, suffix=".zip", prefix="compressed_")
                zip_path = zip_path_obj.name
                zip_path_obj.close()
                with zipfile.ZipFile(zip_path, 'w') as zipf:
                    for a_file in os.listdir(output_dir):
                        zipf.write(os.path.join(output_dir, a_file), arcname=a_file)
                status_message = f"上传模式压缩完成：成功 {success_count} 张，失败 {error_count} 张。ZIP文件已生成。"
                return zip_path, status_message
        finally:
            # 延迟删除临时目录，确保 Gradio 处理完成
            if os.path.exists(output_dir):
                threading.Timer(10, shutil.rmtree, args=[output_dir]).start()
    # 路径模式
    elif folder_path and os.path.isdir(folder_path):
        base_name = custom_output_name if custom_output_name else f'压缩后_{short_side}_{quality}'
        output_folder = os.path.join(custom_output_path, base_name) if custom_output_path else folder_path.rstrip(os.sep) + f'_{base_name}'
        success_count = 0
        error_count = 0
        for root, _, files_in_dir in os.walk(folder_path):
            relative_path = os.path.relpath(root, folder_path)
            output_subfolder = os.path.join(output_folder, relative_path)
            if not os.path.exists(output_subfolder):
                os.makedirs(output_subfolder)
            for filename in files_in_dir:
                file_path = os.path.join(root, filename)
                if filename.lower().endswith(supported_exts):
                    try:
                        with Image.open(file_path) as img:
                            orig_width, orig_height = img.size
                            if mode == "按比例压缩":
                                new_width = int(orig_width * ratio)
                                new_height = int(orig_height * ratio)
                            else:  # 自定义尺寸模式
                                if width > 0 and height > 0:
                                    new_width = width
                                    new_height = height
                                elif width > 0:  # only width specified
                                    new_height = int(orig_height * (width / orig_width))
                                    new_width = width
                                elif height > 0:  # only height specified
                                    new_width = int(orig_width * (height / orig_height))
                                    new_height = height
                                else:  # fallback to short side mode
                                    if orig_width < orig_height:
                                        new_width = short_side
                                        new_height = int(orig_height * (short_side / orig_width))
                                    else:
                                        new_height = short_side
                                        new_width = int(orig_width * (short_side / orig_height))
                            resized_img = img.resize((new_width, new_height), Image.LANCZOS)
                            output_path = os.path.join(output_subfolder, filename)
                            resized_img.save(output_path, optimize=True, quality=quality)
                        success_count += 1
                    except Exception as e:
                        print(f"处理文件 {file_path} 时出错: {e}")
                        error_count += 1
        status_message = f"路径模式压缩完成：成功压缩 {success_count} 张图片，{error_count} 张图片压缩失败。压缩图片已保存在 {output_folder}。"
        return None, status_message
    else:
        return None, "错误：请上传文件，或在高级选项中提供一个有效的本地文件夹路径。"

# 批量转换界面
with gr.Blocks() as batch_ui:
    gr.Markdown("## 批量图片格式转换")
    gr.Markdown("上传图片进行转换。如果需要，可展开高级选项使用本地文件夹路径进行处理。")
    file_input = gr.File(label="上传文件", file_count="multiple")
    with gr.Accordion("高级/本地路径模式 (可选)", open=False):
        path_input = gr.Textbox(label="文件夹路径", placeholder="如果上方已上传文件，此项将被忽略")
        delete_original_checkbox = gr.Checkbox(label="删除原图片 (仅当使用路径模式时有效)", value=False)
    output_format_dropdown = gr.Dropdown(choices=['jpg', 'png', 'jpeg', 'bmp', 'gif'], value='jpg', label="输出格式")
    submit_btn = gr.Button("开始转换", variant="primary")
    gr.Markdown("---")
    file_output = gr.File(label="下载文件")
    status_output = gr.Textbox(label="转换结果")
    submit_btn.click(
        fn=unified_batch_convert,
        inputs=[file_input, path_input, output_format_dropdown, delete_original_checkbox],
        outputs=[file_output, status_output]
    )

# 批量压缩界面
with gr.Blocks() as compress_ui:
    gr.Markdown("## 批量图片压缩")
    gr.Markdown("上传图片进行压缩，或展开高级选项使用本地文件夹路径。可自定义短边像素和压缩质量。")
    file_input = gr.File(label="上传文件", file_count="multiple")
    with gr.Accordion("高级/本地路径模式 (可选)", open=False):
        path_input = gr.Textbox(label="文件夹路径", placeholder="如果上方已上传文件，此项将被忽略")
    with gr.Row():
        mode_dropdown = gr.Dropdown(choices=["按比例压缩", "自定义尺寸"], value="按比例压缩", label="压缩模式")
        short_side_dropdown = gr.Dropdown(choices=["512","612","768", "1024", "2048"], value="768", label="短边像素（可自定义）", visible=True)
        ratio_dropdown = gr.Dropdown(choices=["25%", "50%", "75%", "自定义"], value="50%", label="压缩比例", visible=True)
    with gr.Row(visible=False) as custom_size_row:
        width_input = gr.Number(label="宽度（像素）", value=0)
        height_input = gr.Number(label="高度（像素）", value=0)
    quality_dropdown = gr.Dropdown(choices=["70", "80", "85", "90", "95"], value="85", label="压缩质量（可自定义）")
    with gr.Accordion("高级输出设置", open=False):
        custom_output_path = gr.Textbox(label="自定义输出文件夹路径", placeholder="留空使用默认路径")
        custom_output_name = gr.Textbox(label="自定义输出文件夹名称", placeholder="留空使用默认名称")
    submit_btn = gr.Button("开始压缩", variant="primary")

    def toggle_size_fields(mode):
        if mode == "按比例压缩":
            return {short_side_dropdown: gr.update(visible=True), custom_size_row: gr.update(visible=False)}
        else:
            return {short_side_dropdown: gr.update(visible=False), custom_size_row: gr.update(visible=True)}
    
    mode_dropdown.change(
        fn=toggle_size_fields,
        inputs=mode_dropdown,
        outputs=[short_side_dropdown, custom_size_row]
    )
    gr.Markdown("---")
    file_output = gr.File(label="下载文件")
    status_output = gr.Textbox(label="压缩结果")
    submit_btn.click(
        fn=compress_images_gradio,
        inputs=[
            file_input, 
            path_input, 
            short_side_dropdown, 
            quality_dropdown,
            width_input,
            height_input,
            mode_dropdown,
            ratio_dropdown,
            custom_output_path,
            custom_output_name
        ],
        outputs=[file_output, status_output]
    )

demo = gr.TabbedInterface([batch_ui, compress_ui], ["批量转换", "批量压缩"])

if __name__ == "__main__":
    demo.launch()
