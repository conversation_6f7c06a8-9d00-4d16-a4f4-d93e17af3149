import os
from PIL import Image
import gradio as gr
import time
import zipfile
import tempfile
import shutil
import schedule
import threading
from datetime import datetime, timedelta
import base64
from zhipuai import ZhipuAI
import pandas as pd
from concurrent.futures import ThreadPoolExecutor

# 第二天凌晨清空服务器上的数据
def clear_server_data():
    output_dirs = ['converted_images', 'compressed_images']
    for output_dir in output_dirs:
        if os.path.exists(output_dir):
            shutil.rmtree(output_dir)
    temp_files = [f for f in os.listdir('.') if f.startswith(('converted_', 'compressed_')) and f.endswith('.zip')]
    for temp_file in temp_files:
        os.remove(temp_file)

# 启动定时任务
def schedule_clear_task():
    now = datetime.now()
    next_day = now + timedelta(days=1)
    next_midnight = next_day.replace(hour=0, minute=0, second=0, microsecond=0)
    seconds_until_midnight = (next_midnight - now).total_seconds()
    time.sleep(seconds_until_midnight)
    clear_server_data()
    schedule.every().day.at("00:00").do(clear_server_data)
    while True:
        schedule.run_pending()
        time.sleep(1)

# 启动定时任务线程
threading.Thread(target=schedule_clear_task, daemon=True).start()

# 批量转换（上传/路径）
def unified_batch_convert(files, folder_path, output_format, delete_original):
    # Mode 1: File Upload (takes precedence)
    if files:
        output_format = output_format.lower()
        save_format = 'JPEG' if output_format in ('jpg', 'jpeg') else output_format.upper()
        ext = 'jpg' if output_format == 'jpg' else output_format
        
        output_dir = tempfile.mkdtemp()
        try:
            success_count = 0
            error_count = 0
            output_files = []

            for temp_file in files:
                original_filename = os.path.basename(temp_file.name)
                file_base, _ = os.path.splitext(original_filename)
                output_filename = f"{file_base}.{ext}"
                output_path = os.path.join(output_dir, output_filename)

                try:
                    with Image.open(temp_file.name) as img:
                        if save_format == 'JPEG':
                            if img.mode != 'RGB':
                                img = img.convert('RGB')
                        img.save(output_path, save_format)
                    success_count += 1
                    output_files.append(output_path)
                except Exception as e:
                    print(f"转换 {original_filename} 时出错: {e}")
                    error_count += 1
            
            if success_count == 0:
                return None, f"所有 {error_count} 张图片都转换失败。"

            if len(output_files) == 1:
                status_message = f"上传模式处理完成：成功 {success_count} 张，失败 {error_count} 张。"
                return output_files[0], status_message
            else:
                zip_path_obj = tempfile.NamedTemporaryFile(delete=False, suffix=".zip", prefix="converted_")
                zip_path = zip_path_obj.name
                zip_path_obj.close()

                with zipfile.ZipFile(zip_path, 'w') as zipf:
                    for a_file in os.listdir(output_dir):
                        zipf.write(os.path.join(output_dir, a_file), arcname=a_file)
                
                status_message = f"上传模式处理完成：成功 {success_count} 张，失败 {error_count} 张。ZIP文件已生成。"
                return zip_path, status_message

        except Exception as e:
            print(f"批量转换时发生意外错误: {e}")
            return None, "处理过程中发生意外错误。"
        finally:
            # 延迟删除临时目录，确保 Gradio 处理完成
            if os.path.exists(output_dir):
                threading.Timer(10, shutil.rmtree, args=[output_dir]).start()


    # Mode 2: Folder Path
    elif folder_path and os.path.isdir(folder_path):
        output_format = output_format.lower()
        success_count = 0
        error_count = 0
        supported_formats = ['jpg', 'png', 'jpeg', 'bmp', 'gif']
        
        if output_format not in supported_formats:
            return None, f"不支持的输出格式: {output_format}。"

        for root, _, files_in_dir in os.walk(folder_path):
            for file in files_in_dir:
                try:
                    file_ext = os.path.splitext(file)[1].lower()[1:]
                    if file_ext in supported_formats and file_ext != output_format:
                        input_path = os.path.join(root, file)
                        output_path = os.path.splitext(input_path)[0] + f'.{output_format}'
                        
                        with Image.open(input_path) as img:
                            if output_format in ('jpg', 'jpeg'):
                                if img.mode != 'RGB':
                                    img = img.convert('RGB')
                            save_format = 'JPEG' if output_format == 'jpg' else output_format.upper()
                            img.save(output_path, save_format)
                        
                        success_count += 1
                        if delete_original:
                            os.remove(input_path)
                except Exception as e:
                    print(f"转换 {file} 时出错: {e}")
                    error_count += 1
        
        status_message = f"路径模式处理完成：成功转换 {success_count} 张图片，{error_count} 张图片转换失败。"
        return None, status_message
    
    # No valid input
    else:
        return None, "错误：请上传文件，或在高级选项中提供一个有效的本地文件夹路径。"

# 批量压缩（上传/路径）
def compress_images_gradio(files, folder_path, short_side, quality, width=0, height=0, mode="按比例压缩"):
    short_side = int(short_side) if short_side else 0
    quality = int(quality)
    width = int(width) if width else 0
    height = int(height) if height else 0
    supported_exts = ('.png', '.jpg', '.jpeg', '.gif')
    # 上传模式
    if files:
        output_dir = tempfile.mkdtemp()
        try:
            success_count = 0
            error_count = 0
            output_files = []

            for temp_file in files:
                original_filename = os.path.basename(temp_file.name)
                file_base, ext = os.path.splitext(original_filename)
                output_filename = f"{file_base}{ext}"
                output_path = os.path.join(output_dir, output_filename)
                try:
                    with Image.open(temp_file.name) as img:
                        orig_width, orig_height = img.size
                        if mode == "按比例压缩":
                            if orig_width < orig_height:
                                new_width = short_side
                                new_height = int(orig_height * (short_side / orig_width))
                            else:
                                new_height = short_side
                                new_width = int(orig_width * (short_side / orig_height))
                        else:  # 自定义尺寸
                            if width > 0 and height > 0:
                                new_width = width
                                new_height = height
                            elif width > 0:  # 只指定宽度
                                new_height = int(orig_height * (width / orig_width))
                                new_width = width
                            elif height > 0:  # 只指定高度
                                new_width = int(orig_width * (height / orig_height))
                                new_height = height
                            else:  # 回退到比例模式
                                if orig_width < orig_height:
                                    new_width = short_side
                                    new_height = int(orig_height * (short_side / orig_width))
                                else:
                                    new_height = short_side
                                    new_width = int(orig_width * (short_side / orig_height))
                        resized_img = img.resize((new_width, new_height), Image.LANCZOS)
                        resized_img.save(output_path, optimize=True, quality=quality)
                    success_count += 1
                    output_files.append(output_path)
                except Exception as e:
                    print(f"压缩 {original_filename} 时出错: {e}")
                    error_count += 1
            if success_count == 0:
                return None, f"所有 {error_count} 张图片都压缩失败。"

            if len(output_files) == 1:
                status_message = f"上传模式压缩完成：成功 {success_count} 张，失败 {error_count} 张。"
                return output_files[0], status_message
            else:
                zip_path_obj = tempfile.NamedTemporaryFile(delete=False, suffix=".zip", prefix="compressed_")
                zip_path = zip_path_obj.name
                zip_path_obj.close()
                with zipfile.ZipFile(zip_path, 'w') as zipf:
                    for a_file in os.listdir(output_dir):
                        zipf.write(os.path.join(output_dir, a_file), arcname=a_file)
                status_message = f"上传模式压缩完成：成功 {success_count} 张，失败 {error_count} 张。ZIP文件已生成。"
                return zip_path, status_message
        finally:
            # 延迟删除临时目录，确保 Gradio 处理完成
            if os.path.exists(output_dir):
                threading.Timer(10, shutil.rmtree, args=[output_dir]).start()
    # 路径模式
    elif folder_path and os.path.isdir(folder_path):
        output_folder = folder_path.rstrip(os.sep) + f'_压缩后_{short_side}_{quality}'
        success_count = 0
        error_count = 0
        for root, _, files_in_dir in os.walk(folder_path):
            relative_path = os.path.relpath(root, folder_path)
            output_subfolder = os.path.join(output_folder, relative_path)
            if not os.path.exists(output_subfolder):
                os.makedirs(output_subfolder)
            for filename in files_in_dir:
                file_path = os.path.join(root, filename)
                if filename.lower().endswith(supported_exts):
                    try:
                        with Image.open(file_path) as img:
                            orig_width, orig_height = img.size
                            if mode == "按比例压缩":
                                if orig_width < orig_height:
                                    new_width = short_side
                                    new_height = int(orig_height * (short_side / orig_width))
                                else:
                                    new_height = short_side
                                    new_width = int(orig_width * (short_side / orig_height))
                            else:  # 自定义尺寸
                                if width > 0 and height > 0:
                                    new_width = width
                                    new_height = height
                                elif width > 0:  # 只指定宽度
                                    new_height = int(orig_height * (width / orig_width))
                                    new_width = width
                                elif height > 0:  # 只指定高度
                                    new_width = int(orig_width * (height / orig_height))
                                    new_height = height
                                else:  # 回退到比例模式
                                    if orig_width < orig_height:
                                        new_width = short_side
                                        new_height = int(orig_height * (short_side / orig_width))
                                    else:
                                        new_height = short_side
                                        new_width = int(orig_width * (short_side / orig_height))
                            resized_img = img.resize((new_width, new_height), Image.LANCZOS)
                            output_path = os.path.join(output_subfolder, filename)
                            resized_img.save(output_path, optimize=True, quality=quality)
                        success_count += 1
                    except Exception as e:
                        print(f"处理文件 {file_path} 时出错: {e}")
                        error_count += 1
        status_message = f"路径模式压缩完成：成功压缩 {success_count} 张图片，{error_count} 张图片压缩失败。压缩图片已保存在 {output_folder}。"
        return None, status_message
    else:
        return None, "错误：请上传文件，或在高级选项中提供一个有效的本地文件夹路径。"

# 图片文字识别功能
DEFAULT_API_KEY = "a95934598bc145328b2a1170e251242e.w5JWPbRPrER3cLYW"
DEFAULT_PROMPT = """# 角色
你是一位图片文字提取整理专家，专为用户从图片中精准提取文字信息，并将其清晰、合理地呈现出来，方便用户进一步使用或存档。

## 技能
### 技能 1: 图片文字提取
1. 接收图片文件后，分析图片格式与内容。
2. 运用先进的图像处理技术和光学字符识别技术，准确识别图片中的中文文字，抛弃所有英文内容。
3. 对提取的中文文字进行整理，去除多余空格和换行符，并依据内容逻辑结构进行排版，如划分段落、整理列表等。

### 技能 2: 文字内容补全
若识别出的中文内容存在不合适或缺漏的情况，依据合理推测补全内容，确保意思完整、表意清晰。

## 目标
从图片中准确提取中文文字，按用户需求整理文字，保证输出文字格式清晰、合理，便于用户使用。

## 限制
- 仅能识别图片中的中文文字，不涉及对图片内容的解读或分析，提取的文字需保持原意，避免因格式化而产生歧义。
- 对于识别到的英文全部抛弃。
- 输出内容必须以纯文本格式呈现。

## 输出格式
提取的文字以纯文本格式输出，根据内容的逻辑结构进行合理排版，如段落划分、列表整理等。

## 工作流程
1. 接收图片文件，分析图片的格式和内容。
2. 使用光学字符识别技术识别图片中的中文文字，抛弃所有英文，确保识别的准确性。
3. 对提取的中文文字进行整理，去除多余的空格和换行符，按照内容的逻辑结构进行排版，若有不合适或缺漏的情况，进行合理补全。

## 示例
### 例子 1
图片内容为一页文档，包含标题、正文和表格。
输出结果：
标题：文档标题
正文：
这是文档的第一段内容，包含了一些重要的信息。接下来是第二段内容，详细说明了相关事项。
表格：
| 项目 | 内容 |
|------|------|
| 项目 1 | 内容 1 |
| 项目 2 | 内容 2 |

### 例子 2
图片内容为一张海报，包含活动信息和联系方式。
输出结果：
活动名称：[活动名称]
活动时间：[具体时间]
活动地点：[具体地点]
联系方式：[电话号码] / [电子邮箱]

### 例子 3
图片内容为一张手写便条，包含简单的留言。
输出结果：
留言内容：
亲爱的[姓名]，请记得[具体事项]，谢谢！
"""

def extract_text_from_image(img_path, api_key, model_name, prompt):
    with open(img_path, 'rb') as img_file:
        img_base = base64.b64encode(img_file.read()).decode('utf-8')

    client = ZhipuAI(api_key=api_key)
    response = client.chat.completions.create(
        model=model_name,
        messages=[
            {
                "role": "user",
                "content": [
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/jpeg;base64,{img_base}"
                        }
                    },
                    {
                        "type": "text",
                        "text": prompt
                    }
                ]
            }
        ]
    )
    return response.choices[0].message.content

def reprocess_text(text, api_key, use_multithreading):
    if use_multithreading:
        with ThreadPoolExecutor(max_workers=5) as executor:
            future = executor.submit(_reprocess_single_text, text, api_key)
            return future.result()
    else:
        return _reprocess_single_text(text, api_key)

def _reprocess_single_text(text, api_key):
    client = ZhipuAI(api_key=api_key)
    response = client.chat.completions.create(
        model="GLM-4-Flash-250414",
        messages=[
            {"role": "user", "content": """# 角色
你是一位专业的文本处理专家，具备深厚的语言功底和丰富的文本处理经验。擅长对输入的文本进行补充梳理，使文字内容通顺自然，在不改变文本原意的前提下，精准地进行文字补充优化，且只输出中文内容。

## 技能
### 技能 1: 文本补充梳理
1. 接收用户输入的一段文本。
2. 对输入文本进行细致分析，判断语句是否通顺、逻辑是否连贯。
3. 根据分析结果，在不改变原意的情况下，合理补充文字，优化文本表述，使其通顺流畅。
4. 判断文本是否重复，如存在重复内容，进行合理删减。

## 限制:
- 只能输出中文内容。
- 严格遵循不改变文本原意的原则进行处理。
- 输出的文本应符合正常的语言表达习惯，避免出现生硬或不合理的表述。"""},
            {"role": "user", "content": text}
        ]
    )
    return response.choices[0].message.content

def process_images(images, api_key, use_multithreading, model_name, custom_excel_name, use_custom_name, prompt, reprocess_text_flag):
    if not api_key:
        api_key = DEFAULT_API_KEY

    if not images:
        return "未提供图片", None

    if isinstance(images, list) and len(images) > 1:
        results = []
        if use_multithreading:
            with ThreadPoolExecutor(max_workers=5) as executor:
                futures = [executor.submit(extract_text_from_image, img.name, api_key, model_name, prompt) for img in images]
                for img, future in zip(images, futures):
                    filename = os.path.basename(img.name)
                    result_text = future.result()
                    if model_name == "glm-4v-plus-0111" and reprocess_text_flag:
                        result_text = reprocess_text(result_text, api_key, use_multithreading)
                    results.append([filename, result_text])
        else:
            for img in images:
                filename = os.path.basename(img.name)
                result_text = extract_text_from_image(img.name, api_key, model_name, prompt)
                if model_name == "glm-4v-plus-0111" and reprocess_text_flag:
                    result_text = reprocess_text(result_text, api_key, use_multithreading)
                results.append([filename, result_text])

        df = pd.DataFrame(results, columns=['图片名称', '识别结果'])
        if use_custom_name and custom_excel_name:
            excel_path = custom_excel_name if custom_excel_name.endswith('.xlsx') else custom_excel_name + '.xlsx'
        else:
            excel_path = "ocr_results.xlsx"
        df.to_excel(excel_path, index=False)
        return None, excel_path
    else:
        img = images[0] if isinstance(images, list) else images
        result_text = extract_text_from_image(img.name, api_key, model_name, prompt)
        if model_name == "glm-4v-plus-0111" and reprocess_text_flag:
            result_text = reprocess_text(result_text, api_key, use_multithreading)
        return result_text, None

# 批量转换界面
with gr.Blocks() as batch_ui:
    gr.Markdown("## 批量图片格式转换")
    gr.Markdown("上传图片进行转换。若需使用本地文件夹路径处理，可展开高级选项。")
    with gr.Group():
        file_input = gr.File(label="上传文件", file_count="multiple")
        with gr.Accordion("高级/本地路径模式 (可选)", open=False):
            path_input = gr.Textbox(label="文件夹路径", placeholder="若上方已上传文件，此项将被忽略")
            delete_original_checkbox = gr.Checkbox(label="删除原图片 (仅路径模式有效)", value=False)
        output_format_dropdown = gr.Dropdown(choices=['jpg', 'png', 'jpeg', 'bmp', 'gif'], value='jpg', label="输出格式")
        submit_btn = gr.Button("开始转换", variant="primary", scale=1)
    
    gr.Markdown("---")
    with gr.Group():
        file_output = gr.File(label="下载文件")
        status_output = gr.Textbox(label="转换结果")
    
    submit_btn.click(
        fn=unified_batch_convert,
        inputs=[file_input, path_input, output_format_dropdown, delete_original_checkbox],
        outputs=[file_output, status_output]
    )

# 批量压缩界面
with gr.Blocks() as compress_ui:
    gr.Markdown("## 批量图片压缩")
    gr.Markdown("上传图片进行压缩，或展开高级选项使用本地文件夹路径。可自定义短边像素和压缩质量。")
    with gr.Group():
        file_input = gr.File(label="上传文件", file_count="multiple")
        with gr.Accordion("高级/本地路径模式 (可选)", open=False):
            path_input = gr.Textbox(label="文件夹路径", placeholder="若上方已上传文件，此项将被忽略")
    with gr.Row():
        mode_dropdown = gr.Dropdown(choices=["按比例压缩", "自定义尺寸"], value="按比例压缩", label="压缩模式")
        short_side_dropdown = gr.Dropdown(choices=["512", "768", "1024", "2048"], value="768", label="短边像素（可自定义）", visible=True)
    with gr.Row(visible=False) as custom_size_row:
        width_input = gr.Number(label="宽度（像素）", value=0)
        height_input = gr.Number(label="高度（像素）", value=0)
    quality_dropdown = gr.Dropdown(choices=["70", "80", "85", "90", "95"], value="85", label="压缩质量（可自定义）")
    submit_btn = gr.Button("开始压缩", variant="primary", scale=1)

    def toggle_size_fields(mode):
        if mode == "按比例压缩":
            return {short_side_dropdown: gr.update(visible=True), custom_size_row: gr.update(visible=False)}
        else:
            return {short_side_dropdown: gr.update(visible=False), custom_size_row: gr.update(visible=True)}
    
    mode_dropdown.change(
        fn=toggle_size_fields,
        inputs=mode_dropdown,
        outputs=[short_side_dropdown, custom_size_row]
    )
    
    gr.Markdown("---")
    with gr.Group():
        file_output = gr.File(label="下载文件")
        status_output = gr.Textbox(label="压缩结果")
    
    submit_btn.click(
        fn=compress_images_gradio,
        inputs=[
            file_input, 
            path_input, 
            short_side_dropdown, 
            quality_dropdown,
            width_input,
            height_input,
            mode_dropdown
        ],
        outputs=[file_output, status_output]
    )

# 图片文字识别界面
with gr.Blocks() as ocr_ui:
    gr.Markdown("# 图片文字提取")
    with gr.Group():
        api_key_input = gr.Textbox(label="API Key", placeholder="请输入您的 API Key", type="password", value=DEFAULT_API_KEY)
        image_input = gr.File(file_types=["image"], label="上传图片", file_count="multiple")
        
        with gr.Row():
            model_selector = gr.Dropdown(
                choices=["GLM-4.1V-Thinking-Flash", "GLM-4V-Flash", "glm-4v-plus-0111"],
                value="GLM-4.1V-Thinking-Flash",
                label="选择模型",
                scale=2
            )
            reprocess_text_checkbox = gr.Checkbox(
                label="再次整理",
                value=False,
                scale=1,
                container=False
            )
        
        text_output = gr.Textbox(label="识别结果", lines=10, interactive=False)
        excel_output = gr.File(label="Excel 结果")
        
        with gr.Row():
            submit_button = gr.Button("提交", variant="primary", scale=2)
            use_multithreading_checkbox = gr.Checkbox(label="多线程", value=False, scale=1)
        
        with gr.Accordion("高级选项", open=False):
            custom_excel_name_input = gr.Textbox(label="自定义 Excel 文件名", placeholder="请输入 Excel 文件名（可选）")
            use_custom_name_checkbox = gr.Checkbox(label="使用自定义文件名", value=False)
            prompt_input = gr.Textbox(label="自定义提示词", value=DEFAULT_PROMPT, lines=10)
    
    submit_button.click(
        fn=process_images,
        inputs=[image_input, api_key_input, use_multithreading_checkbox, model_selector, custom_excel_name_input, use_custom_name_checkbox, prompt_input, reprocess_text_checkbox],
        outputs=[text_output, excel_output]
    )

# 创建标签页界面
demo = gr.TabbedInterface(
    [batch_ui, compress_ui, ocr_ui], 
    ["批量转换", "批量压缩", "图片文字识别"]
)

if __name__ == "__main__":
    demo.launch()
